"""Object registry for dependency injection."""

import functools
from collections.abc import Callable
from typing import ParamSpec, TypeVar

T = TypeVar("T")
P = ParamSpec("P")

instances: dict[type, object] = {}
event_handlers: dict[str, Callable] = {}
instances_to_register: list[tuple[type, list[type] | None, list[object] | None]] = []
event_handlers_to_register: dict[str, Callable] = {}


def locate_instance[T](clazz: type[T] | None = None) -> T | None:
    """Get instance of class from registry."""
    instance = instances.get(clazz)
    if isinstance(instance, clazz):
        return instance
    return None


def register_instance(
    dependencies: list[type] | None = None, arguments: list[object] | None = None
) -> Callable[[type[T]], type[T]]:
    """Register instance of class in registry."""

    def register(cls: type[T]) -> type[T]:
        """Register instance of class in registry."""
        if not dependencies:
            instances[cls] = cls(*arguments) if arguments is not None else cls()
            return cls

        args = [locate_instance(d) for d in dependencies]
        if all(args):
            if arguments:
                args.extend(arguments)
            instances[cls] = cls(*args)
        else:
            instances_to_register.append((cls, dependencies, arguments))
        return cls

    return register


def complete_instance_creation() -> None:
    """Complete instance creation."""
    for cls, dependencies, arguments in instances_to_register:
        args: list[object] = []
        if dependencies:
            args.extend([locate_instance(d) for d in dependencies])
        if arguments:
            args.extend(arguments)
        instances[cls] = cls(*args)


def finalize_app_initialization() -> None:
    """Load all instances to be created."""
    complete_instance_creation()


def inject(**services_to_inject: type) -> Callable[[Callable[P, T]], Callable[P, T]]:
    """Provide dependency injection decorator."""

    def real_decorator(func: Callable[P, T]) -> Callable[P, T]:
        """Wrap function with injected dependencies."""

        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            """Inject dependencies into decorated function."""
            try:
                dependencies = {
                    name: locate_instance(cls)
                    for name, cls in services_to_inject.items()
                }
                return func(*args, **kwargs, **dependencies)
            except Exception as e:
                raise e from e

        return functools.update_wrapper(wrapper, func)

    return real_decorator
