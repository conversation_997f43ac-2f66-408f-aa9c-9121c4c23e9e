[project]
name = "audit-review-processer"
version = "0.1.0"
description = "Audit Review Processer"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "boto3==1.38.29",
    "celery==5.3.6",
    "fastapi==0.115.12",
    "google-api-python-client==2.171.0",
    "google-genai==1.18.0",
    "google-generativeai==0.8.5",
    "httpx==0.28.1",
    "langchain[google-genai]==0.3.25",
    "langchain-community==0.3.25",
    "langchain-google-genai==2.0.10",
    "pandas==2.2.3",
    "pydantic-settings==2.9.1",
    "python-slugify==8.0.4",
    "redis==6.2.0",
    "requests==2.32.3",
    "uvicorn==0.34.2",
    "pydantic==2.11.5",
]

[dependency-groups]
dev = [
    "ruff>=0.12.0",
]
