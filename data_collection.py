import boto3
import psycopg2
import csv
from datetime import datetime, timedelta
import json
import requests
import email
import imaplib
import zipfile
import io
import glob
import mysql.connector
import time
from sshtunnel import SSHTunnelForwarder
import paramiko

# Configuration
PROWL_SECRET_NAME = "treebo/production/aps1-cluster/apps/shared-credentials/postgres"  # Replace with your AWS Secrets Manager secret name
REGION_NAME = "ap-south-1"   # Replace with your AWS region
PROWL_SELECT_QUERY = """
select hotel.hotelogix_code as HX_ID,hotel.cs_id as CSID,hotel.name as hotel,city.name as city,source,screen_name,review_date, reviews.review,score, staff_mentions,
levela.review as issue1,levelb.review as issue2,levelc.review as issue3,leveld.review as issue4,levele.review as issue5, prowl_room.room_number from 
((select * from prowl_webreview
where Review_date between current_date - interval '7 days' and current_date - interval '1 day') reviews
join prowl_hotel hotel
on reviews.hotel=hotel.id
join prowl_city city 
on city.id=hotel.city_id
left join prowl_reviewlevel3mapper levela 
on levela.id = reviews.issue_1_id
left join prowl_reviewlevel3mapper levelb 
on levelb.id = reviews.issue_2_id
left join prowl_reviewlevel3mapper levelc
on levelc.id = reviews.issue_3_id
left join prowl_reviewlevel3mapper leveld
on leveld.id = reviews.issue_4_id
left join prowl_reviewlevel3mapper levele 
on levele.id = reviews.issue_5_id
inner join prowl_room 
on prowl_room.id = hotel.id
) order by Review_date;
"""

PRESS9_SECRET_NAME = "treebo/production/aps1-cluster/apps/press9/mysql"  # Replace with your press9 DB secret name
PRESS9_SELECT_QUERY = """
select distinct to_do_template_id from to_do_todo where date(created_at) between current_date - interval 7 day and current_date - interval 1 day;
"""

API_URL = "https://api-prowl.treebo.com/api/web/checklist-analytics/export-checklist-report/"
API_TOKEN = "b78bf29bd5bc209b362c2c3351c0dd1eeeca48cd"
API_REFERER = "https://app.onpress9.com/"

S3_BUCKET_NAME = "audit-review-processor"  # Set your S3 bucket name here


def get_db_credentials(secret_name, region_name):
    """Retrieve database credentials from AWS Secrets Manager."""
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )
    get_secret_value_response = client.get_secret_value(SecretId=secret_name)
    secret = get_secret_value_response['SecretString']
    return json.loads(secret)


def fetch_data_and_save_to_csv_postgres(credentials, query, filename_prefix):
    """Connect to PostgreSQL via SSH tunnel, run the query, and save results to a CSV file."""
    ssh_host = '***********'
    ssh_user = 'conman'
    ssh_pkey = '~/.ssh/conman_id_rsa'
    remote_bind_address = (
        credentials['prowl_db_host'],
        int(credentials.get('prowl_db_port', 5432))
    )

    with SSHTunnelForwarder(
        (ssh_host, 22),
        ssh_username=ssh_user,
        ssh_pkey=ssh_pkey,
        remote_bind_address=remote_bind_address,
        local_bind_address=('127.0.0.1',)
    ) as tunnel:
        conn = psycopg2.connect(
            host='127.0.0.1',
            port=tunnel.local_bind_port,
            user=credentials['prowl_db_user'],
            password=credentials['prowl_db_pass'],
            dbname=credentials['prowl_db_name']
        )
        cur = conn.cursor()
        cur.execute(query)
        rows = cur.fetchall()
        colnames = [desc[0] for desc in cur.description]
        cur.close()
        conn.close()

        date_str = datetime.now().strftime('%Y-%m-%d')
        filename = f"{filename_prefix}_{date_str}.csv"
        with open(filename, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(colnames)
            writer.writerows(rows)
        print(f"Data saved to {filename}")
        return rows, colnames


def fetch_data_and_save_to_csv_mysql(credentials, query, filename_prefix):
    """Connect to MySQL via SSH (paramiko), run the query, and save results to a CSV file."""
    SSH_HOST = '************'
    SSH_USER = 'deploy'

    MYSQL_HOST = credentials['host']
    MYSQL_USER = credentials['username']
    MYSQL_PASS = credentials['password']
    MYSQL_DB = credentials['dbname']

    # Escape double quotes in the query for shell
    safe_query = query.replace('"', '\"').replace("'", "\\'")
    MYSQL_COMMAND = f"mysql -h {MYSQL_HOST} -u {MYSQL_USER} -p'{MYSQL_PASS}' {MYSQL_DB} -e \"{safe_query}\" --batch --raw --skip-column-names"

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(SSH_HOST, username=SSH_USER)
        print("🔐 SSH connection established.")
        stdin, stdout, stderr = ssh.exec_command(MYSQL_COMMAND)
        output = stdout.read().decode()
        error = stderr.read().decode()
        ssh.close()
        print("🔌 SSH connection closed.")
        if error:
            print(f"❌ MySQL Error: {error.strip()}")
            raise Exception(error.strip())
        # Save output to CSV
        date_str = datetime.now().strftime('%Y-%m-%d')
        filename = f"{filename_prefix}_{date_str}.csv"
        with open(filename, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            for line in output.strip().split('\n'):
                writer.writerow(line.split('\t'))
        print(f"Data saved to {filename}")
        # Return rows as list of lists for compatibility
        rows = [line.split('\t') for line in output.strip().split('\n') if line]
        colnames = []  # Not available with --skip-column-names
        return rows, colnames
    except Exception as e:
        print(f"❌ SSH or command error: {e}")
        return [], []


def call_checklist_api(checklist_ids, after_date, before_date):
    headers = {
        'Authorization': f'Token {API_TOKEN}',
        'Referer': API_REFERER,
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json;charset=UTF-8',
    }
    payload = {
        "task_status": "closed",
        "checklists": checklist_ids,
        "completed_at_after": after_date,
        "completed_at_before": before_date
    }
    response = requests.post(API_URL, headers=headers, json=payload)
    if response.status_code == 200:
        print("API call successful. Saving response.")
        date_str = datetime.now().strftime('%Y-%m-%d')
        with open(f"api_response_{date_str}.json", "w") as f:
            f.write(response.text)
    else:
        print(f"API call failed: {response.status_code} {response.text}")


def download_latest_zip_from_gmail(gmail_user, gmail_password):
    """Download the latest zip file attachment from Gmail inbox matching the subject and sender."""
    mail = imaplib.IMAP4_SSL('imap.gmail.com')
    mail.login(gmail_user, gmail_password)
    mail.select('inbox')
    # Search for emails from the sender with the specific subject
    search_criteria = '(FROM "<EMAIL>" SUBJECT "Checklist Report")'
    result, data = mail.search(None, search_criteria)
    mail_ids = data[0].split()
    if not mail_ids:
        print("No zip emails <NAME_EMAIL> with subject 'Checklist Report'.")
        return None

    # Only consider emails from the last 5 minutes (or whatever logic you want)
    now = datetime.utcnow()
    for mail_id in reversed(mail_ids):  # Check most recent first
        result, data = mail.fetch(mail_id, '(RFC822)')
        raw_email = data[0][1]
        msg = email.message_from_bytes(raw_email)
        for part in msg.walk():
            if part.get_content_maintype() == 'multipart':
                continue
            if part.get('Content-Disposition') is None:
                continue
            filename = part.get_filename()
            if filename and filename.endswith('.zip'):
                print(f"Found zip attachment: {filename}")
                zip_data = part.get_payload(decode=True)
                with open(filename, 'wb') as f:
                    f.write(zip_data)
                return filename
    print("No zip attachment found in the latest email.")
    return None


def extract_and_rename_csvs(zip_filename):
    """Extract CSVs from zip, rename if they have data, ignore if empty (except header). Return list of renamed files."""
    date_str = datetime.now().strftime('%Y-%m-%d')
    renamed_files = []
    with zipfile.ZipFile(zip_filename, 'r') as zip_ref:
        for csv_name in zip_ref.namelist():
            if not csv_name.endswith('.csv'):
                continue
            with zip_ref.open(csv_name) as csvfile:
                lines = csvfile.read().decode('utf-8').splitlines()
                if len(lines) > 1:  # header + at least one data row
                    prefix = csv_name.rsplit('.', 1)[0]
                    new_name = f"{prefix}_{date_str}.csv"
                    with open(new_name, 'w', newline='') as out_csv:
                        out_csv.write('\n'.join(lines))
                    print(f"Saved non-empty CSV: {new_name}")
                    renamed_files.append(new_name)
                else:
                    print(f"Ignored empty CSV: {csv_name}")
    return renamed_files


def upload_csvs_to_s3_by_folder(bucket_name, date_str, press9_files):
    """Upload prowl_data CSV to prowl folder and only renamed (non-empty) press9 CSVs (excluding checklist CSV) to press9 folder in S3."""
    s3 = boto3.client('s3')
    # Define S3 folder paths
    prowl_prefix = f"{date_str}/prowl/input/"
    press9_prefix = f"{date_str}/press9/input/"
    # Upload prowl_data CSV
    prowl_pattern = f"prowl_data_{date_str}.csv"
    for file_path in glob.glob(prowl_pattern):
        file_name = file_path.split('/')[-1]
        s3_key = prowl_prefix + file_name
        try:
            s3.upload_file(file_path, bucket_name, s3_key)
            print(f"Uploaded {file_name} to S3 bucket {bucket_name} at {s3_key}")
        except Exception as e:
            print(f"Failed to upload {file_name}: {e}")
    # Upload only the renamed (non-empty) press9 CSVs, excluding checklist CSV
    for file_path in press9_files:
        file_name = file_path.split('/')[-1]
        if file_name.startswith('checklist_'):
            print(f"Skipping upload of checklist CSV: {file_name}")
            continue
        s3_key = press9_prefix + file_name
        try:
            s3.upload_file(file_path, bucket_name, s3_key)
            print(f"Uploaded {file_name} to S3 bucket {bucket_name} at {s3_key}")
        except Exception as e:
            print(f"Failed to upload {file_name}: {e}")


def main():
    # Step 1: Prowl DB (PostgreSQL)
    prowl_credentials = get_db_credentials(PROWL_SECRET_NAME, REGION_NAME)
    fetch_data_and_save_to_csv_postgres(prowl_credentials, PROWL_SELECT_QUERY, "prowl_data")

    # Step 2: Press9 DB (MySQL)
    press9_credentials = get_db_credentials(PRESS9_SECRET_NAME, REGION_NAME)
    rows, colnames = fetch_data_and_save_to_csv_mysql(press9_credentials, PRESS9_SELECT_QUERY, "checklist")

    # Step 3: Prepare checklist IDs for API
    checklist_ids = [row[0] for row in rows]
    today = datetime.now()
    after_date = (today - timedelta(days=7)).strftime('%Y-%m-%d')
    before_date = (today - timedelta(days=1)).strftime('%Y-%m-%d')
    call_checklist_api(checklist_ids, after_date, before_date)

    # Step 4: Wait 5 minutes before fetching audit data zip from Gmail
    print("Waiting 5 minutes for the audit data email to arrive...")
    time.sleep(300)  # 5 minutes in seconds
    GMAIL_USER = "<EMAIL>"
    GMAIL_PASSWORD = "txocjhhbyjuosugh"
    ZIP_FILENAME = download_latest_zip_from_gmail(GMAIL_USER, GMAIL_PASSWORD)
    press9_files = []
    if ZIP_FILENAME:
        press9_files = extract_and_rename_csvs(ZIP_FILENAME)

    # Step 5: Upload all processed CSVs to S3 in their respective folders
    date_str = datetime.now().strftime('%Y-%m-%d')
    upload_csvs_to_s3_by_folder(S3_BUCKET_NAME, date_str, press9_files)


if __name__ == "__main__":
    main() 
