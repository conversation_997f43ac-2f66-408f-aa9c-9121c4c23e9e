# This file was autogenerated by uv via the following command:
#    uv export --no-hashes --format requirements-txt
amqp==5.3.1
    # via kombu
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   google-genai
    #   httpx
    #   starlette
billiard==4.2.1
    # via celery
boto3==1.38.29
    # via ttt-task
botocore==1.38.29
    # via
    #   boto3
    #   s3transfer
cachetools==5.5.2
    # via google-auth
celery==5.3.6
    # via ttt-task
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   uvicorn
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
colorama==0.4.6 ; sys_platform == 'win32'
    # via
    #   click
    #   tqdm
fastapi==0.115.12
    # via ttt-task
google-ai-generativelanguage==0.6.15
    # via google-generativeai
google-api-core==2.25.0
    # via
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-generativeai
google-api-python-client==2.171.0
    # via
    #   google-generativeai
    #   ttt-task
google-auth==2.40.2
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-genai
    #   google-generativeai
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-genai==1.18.0
    # via ttt-task
google-generativeai==0.8.5
    # via ttt-task
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio==1.73.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via google-api-core
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
httpx==0.28.1
    # via
    #   google-genai
    #   ttt-task
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
kombu==5.5.4
    # via celery
numpy==2.2.6
    # via pandas
packaging==24.2
    # via kombu
pandas==2.2.3
    # via ttt-task
prompt-toolkit==3.0.51
    # via click-repl
proto-plus==1.26.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==5.29.5
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pydantic==2.11.5
    # via
    #   fastapi
    #   google-genai
    #   google-generativeai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.9.1
    # via ttt-task
pyparsing==3.2.3
    # via httplib2
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   celery
    #   pandas
python-dotenv==1.1.0
    # via pydantic-settings
python-slugify==8.0.4
    # via ttt-task
pytz==2025.2
    # via pandas
redis==6.2.0
    # via ttt-task
requests==2.32.3
    # via
    #   google-api-core
    #   google-genai
    #   ttt-task
rsa==4.9.1
    # via google-auth
s3transfer==0.13.0
    # via boto3
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
starlette==0.46.2
    # via fastapi
text-unidecode==1.3
    # via python-slugify
tqdm==4.67.1
    # via google-generativeai
typing-extensions==4.13.2
    # via
    #   fastapi
    #   google-genai
    #   google-generativeai
    #   pydantic
    #   pydantic-core
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via
    #   celery
    #   kombu
    #   pandas
uritemplate==4.2.0
    # via google-api-python-client
urllib3==2.4.0
    # via
    #   botocore
    #   requests
uvicorn==0.34.2
    # via ttt-task
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
websockets==15.0.1
    # via google-genai
