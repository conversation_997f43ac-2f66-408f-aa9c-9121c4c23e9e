exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

line-length = 88
indent-width = 4
target-version = "py313"

[lint]
select = ["ALL"]
ignore = [
    "FBT003",
    "FBT002",
    "FBT001",
    "D203",   # Ignoring D203 to avoid conflict with D211
    "D213",   # Ignoring D213 to avoid conflict with D212
    "COM812"  # Ignoring this rule to avoid formatter conflict
]
fixable = ["ALL"]

[format]
quote-style = "double"
indent-style = "space"
