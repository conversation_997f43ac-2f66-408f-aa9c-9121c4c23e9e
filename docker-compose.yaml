version: "3.3"

services:
  app:
    container_name: "ttt-project-backend"
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "6666:6666"
    entrypoint: /app/scripts/start-prod.sh
    env_file:
      - ./.env
    depends_on:
      - redis

  celery_worker:
    container_name: "ttt-project-celery-worker"
    build:
      context: .
      dockerfile: Dockerfile
    entrypoint: /app/scripts/start-celery.sh
    env_file:
      - ./.env
    depends_on:
      - redis
      - app

  redis:
    container_name: "ttt-project-redis"
    image: redis:7-alpine
    ports:
      - "6379:6379"