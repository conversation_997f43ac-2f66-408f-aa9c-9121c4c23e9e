"""Application module for the audit review processer."""

import logging

from fastapi import FastAPI

from taskify.api.process_data import router as file_upload_router
from taskify.config.logging_config import configure_logging

logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """Create the FastAPI application."""
    app = FastAPI()
    configure_logging()
    register_blueprints(app, url_prefix="/api/v1")
    return app


def register_blueprints(app: FastAPI, url_prefix: str | None = None) -> None:
    """Register the blueprints."""
    app.include_router(file_upload_router, prefix=url_prefix)
