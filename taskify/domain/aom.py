"""Module for processing AOM data."""

import numpy as np
import pandas as pd

from taskify.config.app_config import get_settings
from taskify.domain.common.utils import clean_string, is_datetime, is_number, is_url

exclude_columns = [
    "Date",
    "Timestamp",
    "Time taken",
    "Name",
    "Email address",
    "Phone no.",
    "Site name",
    "Location",
    "Created At",
]

common_columns = ["Created At", "Created By", "Updated At", "Updated By"]


settings = get_settings()


class AOMProcessing:
    """Class for processing AOM data."""

    def _is_valid_value(self, value: str) -> bool:
        """Check if the value is valid."""
        value_str = str(value).strip()
        return is_number(value_str) or is_url(value_str) or is_datetime(value_str)

    def process(self, aom_df: pd.DataFrame) -> list[dict]:
        """Process the AOM data."""
        issue_list = []
        seen_issues = set()
        aom_df = aom_df.replace({np.nan: None})
        cols_to_check = [
            col
            for col in aom_df.columns
            if (col not in exclude_columns) and (col not in common_columns)
        ]
        for _, row in aom_df.iterrows():
            for col in cols_to_check:
                value = row[col]
                hotel_name = row["Site name"]
                if not value:
                    continue
                if not self._is_valid_value(value):
                    issue_key = (
                        clean_string(col),
                        clean_string(value),
                        clean_string(hotel_name),
                    )
                    if issue_key not in seen_issues:
                        seen_issues.add(issue_key)
                        issue_list.append(
                            {
                                "issue": f"{issue_key[0]}:{issue_key[1]}",
                                "hotel_name": issue_key[2],
                            }
                        )
        return issue_list
