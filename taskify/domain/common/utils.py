"""Common utility functions for the domain."""

import re
from urllib.parse import urlparse

from dateutil.parser import parse

from taskify.domain.common.constants import URL_REGEX


def is_datetime(value: str) -> bool:
    """Check if the value is a datetime."""
    if not isinstance(value, str):
        return False
    try:
        parse(value, fuzzy=False)
    except (ValueError, OverflowError):
        return False
    return True


def is_number(value: str) -> bool:
    """Check if the value is a number."""
    number_pattern = re.compile(r"^-?\d+(\.\d+)?([eE][-+]?\d+)?$")
    return bool(number_pattern.match(value))


def is_url(value: str, strict: bool = False) -> bool:
    """Check if the value is a URL."""
    if not isinstance(value, str):
        return False
    parsed = urlparse(value)
    if not parsed.scheme or not parsed.netloc:
        return False
    if strict:
        return bool(URL_REGEX.match(value))
    return True


def clean_string(text: str | None) -> str | None:
    """Clean the string."""
    if not text:
        return None
    stripped_parts = [
        part.strip() for part in text.replace("\n", " ").replace("\r", " ").split()
    ]
    return " ".join(stripped_parts)
