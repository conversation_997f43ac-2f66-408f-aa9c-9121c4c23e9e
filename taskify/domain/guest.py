"""Module for processing guest data."""

import numpy as np
import pandas as pd

from taskify.domain.common.utils import clean_string


class GuestProcessing:
    """Class for processing guest data."""

    def process(self, guest_df: pd.DataFrame) -> list[dict]:
        """Process the guest data."""
        issue_set = set()
        unique_issue_list = []
        guest_df = guest_df.replace({np.nan: None})
        for _, row in guest_df.iterrows():
            hotel_name = clean_string(str(row["hotel"]))
            issue = clean_string(row["review"])
            if not issue:
                continue
            issue_tuple = (issue, hotel_name)
            if issue_tuple not in issue_set:
                issue_set.add(issue_tuple)
                unique_issue_list.append(
                    {
                        "issue": issue,
                        "hotel_name": hotel_name,
                    }
                )
        return unique_issue_list
