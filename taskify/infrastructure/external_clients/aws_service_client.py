"""AWS client class."""

import logging

import boto3
from botocore.client import Config

from taskify.config.app_config import get_settings

settings = get_settings()

logger = logging.getLogger(__name__)


class AWSClient:
    """AWS client class."""

    def __init__(self) -> None:
        """Initialize the AWS client."""
        self.s3_client: type[boto3.client] = self._get_s3_client()

    def _get_s3_client(
        self,
        signature_version: str = settings.AWS_SIGNATURE_VERSION,
        region_name: str = settings.AWS_REGION,
    ) -> boto3.client:
        """Get the S3 client."""
        return boto3.client(
            "s3",
            config=Config(signature_version=signature_version),
            region_name=region_name,
        )

    def upload_file_to_s3(
        self,
        key: str | None = None,
        file_path: str | None = None,
        expiration: int = settings.AWS_S3_URL_EXPIRATION,
    ) -> str:
        """Upload file to S3."""
        self.s3_client.upload_file(file_path, settings.AWS_BUCKET_NAME, key)
        logger.info("File uploaded to s3://%s/%s", settings.AWS_BUCKET_NAME, key)
        return self.s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": settings.AWS_BUCKET_NAME, "Key": key},
            ExpiresIn=expiration,
        )

    def read_aws_file(self, key: str) -> str:
        """Read file from S3."""
        response = self.s3_client.get_object(Bucket=settings.AWS_BUCKET_NAME, Key=key)
        return response["Body"].read().decode("utf-8")

    def folder_exists(self, prefix: str) -> bool:
        """Check if folder exists."""
        response: dict = self.s3_client.list_objects_v2(
            Bucket=settings.AWS_BUCKET_NAME, Prefix=prefix, MaxKeys=1
        )
        return "Contents" in response

    def list_files_in_folder(self, prefix: str) -> list:
        """List files in folder."""
        keys = []
        paginator = self.s3_client.get_paginator("list_objects_v2")
        for page in paginator.paginate(Bucket=settings.AWS_BUCKET_NAME, Prefix=prefix):
            if "Contents" in page:
                page_keys = [
                    str(obj["Key"])
                    for obj in page["Contents"]
                    if not obj["Key"].endswith("/")
                ]
                keys.extend(page_keys)
        return keys
