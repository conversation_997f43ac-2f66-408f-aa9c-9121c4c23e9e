"""Utility functions for the AI client."""

import json
import logging
import re

logger = logging.getLogger(__name__)


def simple_convert(response_text: str) -> list[dict] | None:
    """Convert the response text to JSON."""
    match = re.search(r"```json(.*?)```", response_text, re.DOTALL)
    if match:
        json_str = match.group(1).strip()
    else:
        logger.error("Failed to extract JSON from response: %s", response_text)
        return None
    return json.loads(json_str)
