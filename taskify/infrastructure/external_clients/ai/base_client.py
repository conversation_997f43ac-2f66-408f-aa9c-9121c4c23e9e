"""Base AI client class."""

from abc import ABC, abstractmethod


class BaseAIClient(ABC):
    """Base AI client class."""

    @abstractmethod
    def get_api_key(self) -> str:
        """Get the API key."""
        raise NotImplementedError

    @abstractmethod
    def get_ai_response(self, file_url: str, prompt: str) -> list[dict]:
        """Get the AI response."""
        raise NotImplementedError

    @abstractmethod
    def get_llm_model(self) -> object:
        """Get the LLM model."""
        raise NotImplementedError
