"""Gemini client class."""

import logging
import os

import httpx
from langchain.schema import HumanMessage
from langchain_google_genai import ChatGoogleGenerative<PERSON><PERSON>

from taskify.config.app_config import get_settings
from taskify.infrastructure.external_clients.ai.base_client import BaseA<PERSON>lient
from taskify.infrastructure.external_clients.ai.utils import simple_convert

logger = logging.getLogger(__name__)
settings = get_settings()


class GeminiClient(BaseAIClient):
    """Gemini client class."""

    def __init__(self) -> None:
        """Initialize the Gemini client."""
        os.environ["GOOGLE_API_KEY"] = self.get_api_key()
        self.llm: ChatGoogleGenerativeAI = self.get_llm_model()

    def get_llm_model(self) -> ChatGoogleGenerativeAI:
        """Get the LLM model."""
        return ChatGoogleGenerativeAI(
            model=settings.LLM_MODEL,
            temperature=0.1,
            timeout=60,
        )

    def chunk_csv_data(self, csv_text: str, chunk_size: int) -> list[str]:
        """Chunk the CSV data."""
        lines = csv_text.strip().split("\n")
        if not lines:
            return []
        header = lines[0]
        data_lines = lines[1:]
        chunks = []
        for i in range(0, len(data_lines), chunk_size):
            chunk_lines = [header, *data_lines[i : i + chunk_size]]
            chunks.append("\n".join(chunk_lines))
        return chunks

    def get_ai_response(
        self, file_url: str, prompt: str, chunk_size: int = 250
    ) -> list[dict]:
        """Get the AI response."""
        final_response = []
        response = httpx.get(file_url, timeout=30)
        response.raise_for_status()
        chunks = self.chunk_csv_data(response.text, chunk_size)
        logger.info("Processing %d chunks with %d rows each", len(chunks), chunk_size)
        for i, chunk in enumerate(chunks):
            logger.info("Processing chunk %d", (i + 1))
            combined_content = f"{prompt}CSV Data:\n{chunk}"
            message = HumanMessage(content=combined_content)
            result = self.llm.invoke([message])
            converted_result = simple_convert(result.content)
            logger.info("Converted result: %s", converted_result)
            if converted_result:
                final_response.extend(converted_result)
            logger.info("Processed chunk %d", (i + 1))
        logger.info("Final response: %s", final_response)
        return final_response

    def get_api_key(self) -> str:
        """Get the API key."""
        return settings.LLM_API_KEY
