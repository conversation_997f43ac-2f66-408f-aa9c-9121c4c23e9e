"""Prompt for the AI model."""

import json

from taskify.domain.common.constants import ISSUE_TAGGING


def make_prompt() -> str:
    """Generate the prompt for the AI model."""
    tagging = ISSUE_TAGGING
    sample_response = json.dumps(
        [
            {"issue": "...", "hotel_name": "..."},
            {"issue": "...", "hotel_name": "..."},
        ],
        indent=0,
    )

    output_response = json.dumps(
        [
            {
                "hotel_name": "...",
                "theme": ["...", "...", "..."],
                "HSE": ["Action 1", "Action 2"],
                "MSE": ["Action 3"],
                "LSE": ["Action 4", "Action 5"],
            }
        ],
        indent=0,
    )

    return (
        "You are a hotel operations analyst. Your task is to review and analyze the "
        "following data to extract actionable operational insights, categorized under "
        "predefined issue themes and tags.\n"
        "### INPUT DATA:\n\n"
        "- Data: I have shared the data with you. It's an attachment file.\n"
        "  The format is a CSV file.\n"
        f"- Tagging Information: {tagging}\n\n"
        "### ANALYSIS INSTRUCTIONS:\n\n"
        "1. Use the data and tagging to generate insights.\n"
        "2. The input data is a list of issues in the following format:\n"
        f"{sample_response}\n"
        "3. Extract the hotel_name from each issue.\n"
        "4. Assign an appropriate theme to each issue using the tagging.\n"
        "5. For each issue:\n"
        "   - Assign a priority level:\n"
        "     - HSE: High (Major Issue)\n"
        "     - MSE: Medium (Moderate Issue)\n"
        "     - LSE: Low (Minor Issue)\n"
        "   - Propose a clear, specific and implementable action.\n"
        "6. Do not propose an action if:\n"
        "   - The issue is already described positively.\n"
        "   - The issue contains personal information (names, staff, guests,\n"
        "     or any staff position).\n"
        "7. Only include themes that have at least one actionable item.\n"
        "8. Avoid vague or generic suggestions — be precise and practical.\n"
        "9. Group all issues by hotel_name.\n"
        "10. Tag each issue with the appropriate theme from the tagging info.\n"
        "11. Tagging must be done with a proper precision.\n\n"
        "### OUTPUT FORMAT:\n\n"
        "Respond with only this JSON structure (no explanations, no markdown,\n"
        "no code block): "
        f"{output_response}\n\n"
        "### IMPORTANT:\n\n"
        "- If the issue is empty or does not contain any actionable information,\n"
        "  do not include it in the output. Please skip it.\n"
        "- I will be sending you the data in chunks. Please process each chunk\n"
        "  independently and send the response for that chunk. Always send a\n"
        "  complete JSON response for each chunk.\n"
        "- If for a hotel there are no issues, please do not include that hotel\n"
        "  in the output. Always send a complete JSON response for each chunk.\n"
        "  (!!It is very very important!!)\n"
        "- Output only the JSON — no markdown, no code blocks, no explanation.\n"
        "- Do not wrap the response in triple backticks or any other formatting.\n"
        "- Ensure the output is valid JSON.\n"
        "- The response must only be the JSON structure.\n"
        "- Remove all the whitespace from the JSON response. Send a very\n"
        "  minified JSON response. Keep the indentation to 0 and no spaces.\n"
        "- Always send a complete JSON response for each chunk.\n"
        "  (!!It is very very important!!)\n"
        "- Themes cannot contain HSE, MSE, LSE. They should be proper themes\n"
        "  and not HSE, MSE, LSE.\n"
    )
