"""Email client class."""

import logging
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from taskify.infrastructure.external_clients.email.types import EmailCredentials

logger = logging.getLogger(__name__)


class EmailClient:
    """Email client class."""

    @staticmethod
    def send_email(
        credentials: EmailCredentials,
        recipient_email: list[str],
        subject: str,
        body: str,
        is_html: bool = False,
    ) -> None:
        """Send an email."""
        sender_email = credentials["email"]
        sender_password = credentials["password"]

        msg = MIMEMultipart()
        msg["From"] = sender_email
        msg["To"] = ",".join(recipient_email)
        msg["Subject"] = subject
        body_type = "html" if is_html else "plain"
        msg.attach(MIMEText(body, body_type))

        server = smtplib.SMTP("smtp.gmail.com", 587)
        server.starttls()
        server.login(sender_email, sender_password)
        text = msg.as_string()
        server.sendmail(sender_email, recipient_email, text)
        server.quit()
        logger.info("Email sent successfully!")
