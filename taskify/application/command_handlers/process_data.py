"""Command handlers for processing data."""

import io
import logging
import random
from datetime import UTC, date, datetime
from typing import Any

import pandas as pd

from object_registry import register_instance
from taskify.api.request.payload import ProcessDataRequest
from taskify.application.utils import convert_to_csv
from taskify.config.app_config import get_settings
from taskify.domain.aom import AOMProcessing
from taskify.domain.common.constants import html_body, html_subject
from taskify.domain.guest import GuestProcessing
from taskify.infrastructure.external_clients.ai.gemini_client import GeminiClient
from taskify.infrastructure.external_clients.aws_service_client import AWSClient
from taskify.infrastructure.external_clients.email.email_service_client import (
    EmailClient,
)
from taskify.infrastructure.external_clients.email.types import EmailCredentials

settings = get_settings()

logger = logging.getLogger(__name__)


@register_instance(dependencies=[AWSClient, GeminiClient, EmailClient, ])
class ProcessDataHandler:
    """Handler for processing data."""

    def __init__(
        self,
        aws_client: AWSClient,
        ai_client: <PERSON><PERSON>lient,
        email_client: EmailClient,
        
    ) -> None:
        """Initialize the various clients needed for processing data."""
        self.aws_client = aws_client
        self.ai_client = ai_client
        self.email_client = email_client

    def handle(self, request_data: ProcessDataRequest, run_async: bool) -> None:
        """Handle the request to process data, either asynchronously or synchronously."""
        if run_async:
            logger.info("Running data processing asynchronously.")
            
        else:
            logger.info("Running data processing synchronously.")
            self.handle_process_data(request_data)

    def handle_process_data(self, request_data: ProcessDataRequest) -> None:
        """Handle the request to process data."""
        date_str = request_data.date.strftime("%Y-%m-%d")
        now_str = datetime.now(UTC).strftime("%Y-%m-%d-%H-%M-%S")
        issues = self.get_issue_list(
            date=request_data.date,
            process_prowl=request_data.process_prowl,
            process_press9=request_data.process_press9,
        )
        if request_data.limit:
            issues = self.shuffle_list_with_limit(issues, request_data.limit)
        logger.info("Number of issues: %d", len(issues))
        filepath = convert_to_csv(issues, res_file_name="input")
        input_file_url_for_ai_processing = self.aws_client.upload_file_to_s3(
            key=date_str + "/" + filepath.name,
            file_path=filepath,
        )
        response = self.ai_client.get_ai_response(
            file_url=input_file_url_for_ai_processing,
            prompt=request_data.prompt,
        )
        ai_response_file = convert_to_csv(
            data=response,
            res_file_name="output",
            response=True,
        )

        ai_response_file_url = self.aws_client.upload_file_to_s3(
            key=f"{date_str}/{now_str}-output.csv",
            file_path=ai_response_file,
        )

        self.email_client.send_email(
            credentials=EmailCredentials(
                email=settings.EMAIL_SENDER,
                password=settings.EMAIL_SENDER_PASSWORD,
            ),
            recipient_email=settings.EMAIL_RECIPIENTS,
            subject=html_subject,
            body=html_body.format(
                url=ai_response_file_url,
            ),
            is_html=True,
        )

    def get_issue_list(
        self,
        date: "date",
        process_prowl: bool,
        process_press9: bool,
    ) -> list:
        """Get the list of issues for the given date."""
        issues = []
        date_str = date.strftime("%Y-%m-%d")
        logger.info(
            "Processing data for %s, prowl: %s, press9: %s",
            date_str,
            process_prowl,
            process_press9,
        )
        if not self.aws_client.folder_exists(prefix=date_str):
            logger.info("Folder %s does not exist. Skipping...", date_str)
            return []
        for source in ["prowl", "press9"]:
            logger.info("Processing %s data for %s", source, date_str)
            folder_prefix = f"{date_str}/{source}/input/"
            if not self.aws_client.folder_exists(prefix=folder_prefix):
                logger.info("Folder %s does not exist. Skipping...", folder_prefix)
                continue

            files = self.aws_client.list_files_in_folder(folder_prefix)
            for file in files:
                if source == settings.SOURCE_PROWL_KEY and process_prowl:
                    logger.info("Processing file: %s", file)
                    csv_data = self.aws_client.read_aws_file(key=file)
                    df = pd.read_csv(io.StringIO(csv_data))
                    issues += GuestProcessing().process(df)
                elif source == settings.SOURCE_PRESS9_KEY and process_press9:
                    logger.info("Processing file: %s", file)
                    csv_data = self.aws_client.read_aws_file(key=file)
                    df = pd.read_csv(io.StringIO(csv_data))
                    issues += AOMProcessing().process(df)
        return issues

    def shuffle_list_with_limit(self, input_list: list[Any], limit: int = 500) -> list:
        """Shuffle the list and return a list with a maximum of `limit` elements."""
        length = min(len(input_list), limit)
        return random.sample(input_list, length)
