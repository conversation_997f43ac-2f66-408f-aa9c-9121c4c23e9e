"""Utility functions for the application."""

import pathlib
from datetime import UTC, datetime

import pandas as pd

from taskify.config.app_config import get_settings

settings = get_settings()

TMP_PATH = "tmp"
ROOT_DIR = settings.ROOT_DIR


def clean_list_fields(data: list[dict]) -> str:
    """Clean the list fields."""
    for entry in data:
        for key, value in entry.items():
            if isinstance(value, list):
                entry[key] = ", ".join(value)
    return data


def convert_to_csv(
    data: list, res_file_name: str | None = None, response: bool = False
) -> pathlib.Path:
    """Convert the data to CSV."""
    if response:
        data = clean_list_fields(data)
    df = pd.DataFrame(data)
    now = datetime.now(UTC)
    path = ROOT_DIR / TMP_PATH
    pathlib.Path(path).mkdir(parents=True, exist_ok=True)
    filepath = path / f"{now.strftime('%Y-%m-%d-%H-%M-%S')}-{res_file_name}.csv"
    df.to_csv(str(filepath), index=False)
    return filepath
