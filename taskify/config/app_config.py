"""Module for application configuration."""

import os
import pathlib
from enum import Enum
from functools import lru_cache

from pydantic_settings import BaseSettings


class AppEnv(str, Enum):
    """Enum for application environment."""

    LOCAL = "local"
    DEV = "dev" | "development"
    QA = "qa"
    PROD = "prod" | "production"


class DefaultConfig(BaseSettings):
    """Default configuration for the application."""

    APP_ENV: AppEnv = os.environ.get("APP_ENV", AppEnv.LOCAL)
    ROOT_DIR: pathlib.Path = pathlib.Path(__file__).parent.parent.parent
    DEBUG: bool = APP_ENV != AppEnv.PROD
    TESTING: bool = APP_ENV == AppEnv.QA

    AWS_REGION: str = os.environ.get("AWS_REGION", "ap-south-1")
    AWS_BUCKET_NAME: str = os.environ.get("AWS_BUCKET_NAME", "audit-review-processor")
    AWS_S3_URL_EXPIRATION: int = int(os.environ.get("AWS_S3_URL_EXPIRATION", "86400"))
    AWS_SIGNATURE_VERSION: str = os.environ.get("AWS_SIGNATURE_VERSION", "s3v4")

    CELERY_BROKER_URL: str = os.environ.get("CELERY_BROKER_URL", "redis://redis:6379/0")
    CELERY_RESULT_BACKEND: str = os.environ.get(
        "CELERY_RESULT_BACKEND", "redis://redis:6379/0"
    )

    SOURCE_PRESS9_KEY: str = "press9"
    SOURCE_PROWL_KEY: str = "prowl"

    EMAIL_SENDER: str = "<EMAIL>"
    EMAIL_SENDER_PASSWORD: str = os.environ.get(
        "EMAIL_SENDER_PASSWORD", "txocjhhbyjuosugh"
    )
    EMAIL_RECIPIENTS: list[str] = [
        "<EMAIL>",
        # "<EMAIL>",
    ]

    LLM_API_KEY: str = "AIzaSyA2CrFBQzoa9dZGD9CvvCYSdmdS9ebveB0"
    LLM_MODEL: str = "gemini-2.0-flash"


@lru_cache
def get_settings() -> DefaultConfig:
    """Get the settings for the application."""
    return DefaultConfig()
