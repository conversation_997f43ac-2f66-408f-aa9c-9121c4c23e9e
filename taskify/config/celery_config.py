"""Celery configuration for the worker."""

from taskify.config.app_config import get_settings

settings = get_settings()

broker_url = settings.CELERY_BROKER_URL
result_backend = settings.CELERY_RESULT_BACKEND
task_serializer = "json"
result_serializer = "json"
accept_content = ["json"]
timezone = "UTC"
enable_utc = True

include = ["taskify.tasks.process_data_tasks"]
imports = ("taskify.tasks.process_data_tasks",)

task_routes = {
    "taskify.tasks.process_data": {"queue": "data_processing"},
}

task_default_queue = "default"
