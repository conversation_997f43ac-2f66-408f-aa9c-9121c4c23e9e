import logging
from abc import ABC, abstractmethod
from typing import Type, Dict, Any, Callable
from pydantic import BaseModel
from taskify.celery_app import celery_app

logger = logging.getLogger(__name__)


class CeleryTaskRunner:
    task_name: str
    request_model: Type[BaseModel]
    handler_function: Callable

    def __init__(self) -> None:
        self.register()

    @classmethod
    def run_task(cls, request_data_dict: Dict[str, Any]) -> dict:
        try:
            logger.info("Running task: %s", cls.task_name)
            request_data = cls.request_model(**request_data_dict)
            result = cls.handler_function(request_data, run_async=False)
            return {
                "status": "success",
                "message": f"Task `{cls.task_name}` completed successfully",
                "result": result
            }
        except Exception as e:
            logger.exception("Error in task: %s", cls.task_name)
            return {
                "status": "error",
                "message": str(e),
            }

    @classmethod
    def register(cls):
        """Register the task with <PERSON><PERSON><PERSON>."""

        @celery_app.task(name=cls.task_name)
        def celery_task(request_data_dict: Dict[str, Any]) -> dict:
            return cls.run_task(request_data_dict)

        cls.celery_task = celery_task
