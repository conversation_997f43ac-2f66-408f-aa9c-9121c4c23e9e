# """Tasks for processing data."""

# import logging
# from taskify.api.request.payload import ProcessDataRequest
# from taskify.application.command_handlers.process_data import ProcessDataHandler
# from taskify.celery_app import celery_app

# logger = logging.getLogger(__name__)

# @celery_app.task(name="process_data")
# def process_data_task(request_data_dict: dict) -> dict:
#     """Task to process data."""
#     try:
#         request_data = ProcessDataRequest(**request_data_dict)
#         ProcessDataHandler().handle(request_data)
#     except Exception as e:
#         logger.exception("Error processing data")
#         return {"status": "error", "message": str(e)}
#     return {
#         "status": "success",
#         "message": "Data processing completed successfully",
#     }




# taskify/application/async_jobs/process_data.py

from taskify.api.request.payload import ProcessDataRequest
from taskify.application.command_handlers.process_data import ProcessD<PERSON><PERSON>andler
from taskify.async_job.base_job_client import BaseTaskRunner


class ProcessDataTask(BaseTaskRunner):
    task_name = "process_data"
    request_model = ProcessDataRequest
    handler_class = ProcessDataHandler


# Register the Celery task
# ProcessDataTask.register()
