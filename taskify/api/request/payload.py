"""Payloads for API requests."""

from datetime import date, datetime

from pydantic import BaseModel, field_validator

from taskify.infrastructure.external_clients.ai.prompt import make_prompt


class ProcessDataRequest(BaseModel):
    """A Pydantic model for processing data."""

    process_prowl: bool = True
    process_press9: bool = True
    date: date
    prompt: str = make_prompt()
    limit: int | None = None

    @field_validator("date", mode="before")
    @classmethod
    def parse_date(cls, v: str | None) -> "date":
        """Parse the date from a string to a date object."""
        if isinstance(v, str):
            return datetime.strptime(v, "%Y-%m-%d").astimezone().date()
        return v
