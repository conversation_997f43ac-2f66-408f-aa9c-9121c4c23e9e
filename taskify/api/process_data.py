"""API for processing data."""

import logging
from typing import Annotated

from fastapi import API<PERSON><PERSON><PERSON>, Body, Query

from taskify.api.request.payload import ProcessDataRequest
from taskify.application.command_handlers.process_data import ProcessData<PERSON>andler
from taskify.async_job.process_data_tasks import process_data_task

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("")
def upload_file(
    request_data: Annotated[
        ProcessDataRequest,
        Body(..., description="Request data for processing data"),
    ],
    _async: Annotated[bool, Query(True, description="Run processing asynchronously")],
) -> dict:
    """Process data for the given date."""
    try:
        if _async:
            request_dict = request_data.model_dump()
            task = process_data_task.delay(request_dict)
            message = (
                "Data processing initiated. Check email for results. "
                f"Task ID: {task.id}"
            )
        else:
            ProcessDataHandler().handle(request_data)
            message = "Data processing completed. Check email for results."

    except Exception:
        logger.exception("Error processing data")
        return {"error": "Failed to process data. Please check the logs or try again."}
    return {"message": message}
